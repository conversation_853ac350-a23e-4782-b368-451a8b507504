RELACJE MIĘDZY ELEMENTAMI - TASK POPOVER IMPROVEMENTS

=== KOMPONENTY GŁÓWNE ===

TaskPopover (src/components/tasks/task-popover.tsx)
├── Używa: EditableCell (src/components/dashboard/editable-cell.tsx)
├── Używa: QuickTimeEntry (src/components/tasks/quick-time-entry.tsx) [NOWY]
├── Używa: TaskDetailsForm (src/components/tasks/task-details-form.tsx) [NOWY]
├── Używa: shadcn/ui components (Button, Badge, Avatar, Progress, Popover)
├── Używa: lucide-react icons
└── Używa: sonner (toast notifications)

QuickTimeEntry (src/components/tasks/quick-time-entry.tsx) [NOWY]
├── Używa: shadcn/ui components (Button, Select, Input)
├── Używa: lucide-react icons (Plus, Check, X)
├── Używa: sonner (toast notifications)
└── API: POST /api/tasks/[taskId]/time-entries

TaskDetailsForm (src/components/tasks/task-details-form.tsx) [NOWY]
├── Używa: shadcn/ui components (Button, Input, Label, Select, Calendar, Popover, Avatar, Badge)
├── Używa: lucide-react icons (CalendarIcon, Check, X, Edit3)
├── Używa: date-fns (format, pl locale)
├── Używa: sonner (toast notifications)
└── Props: task, users, taskStatuses, onSave, onCancel, disabled

EditableCell (src/components/dashboard/editable-cell.tsx)
├── Używa: shadcn/ui components (Input, Select, Button, Calendar, Popover)
├── Używa: lucide-react icons
├── Używa: sonner (toast notifications)
└── Obsługuje typy: text, number, date, priority, status, user

=== KOMPONENTY RODZICIELSKIE ===

TasksWeeklyCalendar (src/components/tasks/tasks-weekly-calendar.tsx)
├── Zawiera: TaskPopover
├── Przekazuje props: onTaskUpdate, teamMembers, canEdit
├── Funkcje: canEditTask(), handleTaskClick()
└── Używany przez: TasksContent

CalendarContent (src/components/calendar/calendar-content.tsx)
├── Zawiera: TaskPopover
├── Przekazuje props: onTaskUpdate, users, canEdit
├── Funkcje: canEditTask(), handleTaskUpdate(), handleTimeLogged()
└── Pobiera: users, session, tasks

TasksContent (src/components/tasks/tasks-content.tsx)
├── Zawiera: TasksWeeklyCalendar
├── Przekazuje props: onTaskUpdate, teamMembers
├── Funkcje: handleTaskUpdate()
└── Używany przez: Dashboard

=== TYPY I INTERFEJSY ===

TaskUpdateData (src/types/index.ts) [NOWY]
├── Extends: Partial<Task>
├── Dodatkowe pola: assigneeId?, statusId?, projectId?
└── Używany przez: wszystkie funkcje aktualizacji zadań

TaskPopoverProps
├── task: Task
├── onTaskUpdate?: (taskId: string, updates: TaskUpdateData) => void [NOWY]
├── onTimeLogged?: () => void [NOWY]
├── users?: User[] [NOWY]
├── canEdit?: boolean [NOWY]
└── Pozostałe props: children, onTaskClick, onEdit, onTimeTracking, side, align

QuickTimeEntryProps [NOWY]
├── task: Task
├── onTimeLogged: () => void
└── disabled?: boolean

=== API ENDPOINTS ===

PATCH /api/tasks/[taskId]
├── Aktualizacja pól zadania
├── Body: TaskUpdateData
├── Używany przez: handleTaskUpdate functions
└── Response: { task: Task }

POST /api/tasks/[taskId]/time-entries
├── Dodawanie wpisu czasu
├── Body: { hours: number, description?: string, date: string }
├── Używany przez: QuickTimeEntry
└── Response: { timeEntry: TimeEntry }

GET /api/users
├── Pobieranie listy użytkowników
├── Używany przez: CalendarContent
└── Response: { users: User[] }

=== PRZEPŁYW DANYCH ===

1. EDYCJA NAGŁÓWKA ZADANIA:
   User clicks field → EditableCell activates → onSave called →
   handleOptimisticTaskUpdate → API call → Success/Error toast

2. EDYCJA SZCZEGÓŁÓW ZADANIA:
   User clicks "Edytuj" → TaskDetailsForm shows → User edits fields →
   Form submit → handleDetailsFormSave → handleOptimisticTaskUpdate →
   API call → Success toast → Form closes

3. DODAWANIE CZASU:
   User clicks "Dodaj czas" → QuickTimeEntry shows → User selects time →
   Form submit → API call → Success toast → onTimeLogged callback

4. OPTIMISTIC UPDATES:
   User action → Immediate UI update → Loading toast →
   API call → Success toast OR Error toast + rollback

=== UPRAWNIENIA ===

canEditTask(task: Task): boolean
├── Sprawdza: session?.user?.id
├── Warunki: task.createdBy?.id === session.user.id OR task.assignee?.id === session.user.id
└── Używane przez: TasksWeeklyCalendar, CalendarContent

=== POWIADOMIENIA SONNER ===

Loading states:
├── "Aktualizowanie zadania..." (podczas edycji pól)
└── ID: `update-task-${task.id}`

Success states:
├── "Zadanie zostało zaktualizowane" (po edycji)
├── "Czas został zalogowany" (po dodaniu czasu)
└── Automatyczne ukrycie po 2-3 sekundach

Error states:
├── "Nie udało się zaktualizować zadania"
├── "Nie udało się zalogować czasu"
└── Rollback do poprzedniego stanu

=== ZALEŻNOŚCI MIĘDZY PLIKAMI ===

task-popover.tsx
├── IMPORTS: EditableCell, QuickTimeEntry, types, utils
├── EXPORTS: TaskPopover component
└── USED BY: TasksWeeklyCalendar, CalendarContent

quick-time-entry.tsx [NOWY]
├── IMPORTS: shadcn/ui, lucide-react, sonner, types
├── EXPORTS: QuickTimeEntry component
└── USED BY: TaskPopover

editable-cell.tsx
├── IMPORTS: shadcn/ui, lucide-react, sonner, utils
├── EXPORTS: EditableCell component
└── USED BY: TaskPopover, TasksTable

types/index.ts
├── EXPORTS: TaskUpdateData interface [NOWY]
└── USED BY: wszystkie komponenty task-related

=== STYLE I UI ===

TaskPopover:
├── Compact design dla popover
├── Responsive layout
├── Consistent spacing (space-y-2, space-y-3)
└── Accessible keyboard navigation

QuickTimeEntry:
├── Inline form w popover
├── Grid layout dla kompaktowości
├── Visual feedback (loading states)
└── Validation indicators

EditableCell:
├── Inline editing pattern
├── Type-specific inputs
├── Hover states
└── Focus management

=== TESTOWANIE ===

Unit tests potrzebne dla:
├── QuickTimeEntry component
├── TaskPopover editable fields
├── Optimistic updates logic
└── Permission checking functions

Integration tests:
├── End-to-end editing workflow
├── Time logging workflow
├── Error handling scenarios
└── Permission-based access

=== PERFORMANCE ===

Optimalizacje:
├── useCallback dla event handlers
├── Optimistic updates (immediate UI feedback)
├── Debounced API calls (w EditableCell)
└── Minimal re-renders przez proper dependency arrays

Memory management:
├── Cleanup event listeners
├── Proper useEffect dependencies
└── No memory leaks w async operations
